{"name": "ysviewer-electron", "version": "1.0.0", "description": "YSViewer - YouTube and Spotify Viewer with automated playlist management", "main": "src/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "electron . --dev", "test": "jest", "test:watch": "jest --watch", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.ysviewer.app", "productName": "<PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.entertainment"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createStartMenuShortcut": true, "createDesktopShortcut": true, "runAfterFinish": true}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}}, "dependencies": {"auto-launch": "^5.0.6", "aws-sdk": "^2.1691.0", "electron": "^28.0.0", "electron-store": "^8.1.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "electron-builder": "^24.9.1", "jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/main.js"]}, "keywords": ["youtube", "spotify", "playlist", "automation", "electron"], "author": "YSViewer Team", "license": "MIT"}