const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');

// Mock electron module
const mockWebContents = {
  setAudioMuted: jest.fn(),
  isDestroyed: jest.fn(() => false),
  isAudioMuted: jest.fn(() => true)
};

const mockExecAsync = jest.fn();

// Mock child_process
jest.mock('child_process', () => ({
  exec: jest.fn()
}));

// Mock util.promisify
jest.mock('util', () => ({
  promisify: jest.fn(() => mockExecAsync)
}));

// Import the AudioController after mocking
const AudioController = require('../src/audio-controller');

describe('AudioController', () => {
  let audioController;

  beforeEach(() => {
    audioController = new AudioController();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('constructor', () => {
    test('should initialize with correct default values', () => {
      expect(audioController.isMuted).toBe(false);
      expect(audioController.webContents).toEqual([]);
    });
  });

  describe('registerWebContents', () => {
    test('should add web contents to the array', () => {
      audioController.registerWebContents(mockWebContents);
      expect(audioController.webContents).toContain(mockWebContents);
    });

    test('should not add duplicate web contents', () => {
      audioController.registerWebContents(mockWebContents);
      audioController.registerWebContents(mockWebContents);
      expect(audioController.webContents.length).toBe(1);
    });

    test('should not add null or undefined web contents', () => {
      audioController.registerWebContents(null);
      audioController.registerWebContents(undefined);
      expect(audioController.webContents.length).toBe(0);
    });
  });

  describe('unregisterWebContents', () => {
    test('should remove web contents from the array', () => {
      audioController.registerWebContents(mockWebContents);
      audioController.unregisterWebContents(mockWebContents);
      expect(audioController.webContents).not.toContain(mockWebContents);
    });

    test('should handle removing non-existent web contents gracefully', () => {
      const initialLength = audioController.webContents.length;
      audioController.unregisterWebContents(mockWebContents);
      expect(audioController.webContents.length).toBe(initialLength);
    });
  });

  describe('toggleMute', () => {
    test('should toggle mute state from false to true', async () => {
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(audioController.isMuted).toBe(true);
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });

    test('should toggle mute state from true to false', async () => {
      audioController.isMuted = true;
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(audioController.isMuted).toBe(false);
      expect(result).toBe(false);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(false);
    });

    test('should handle destroyed web contents gracefully', async () => {
      const destroyedWebContents = {
        setAudioMuted: jest.fn(),
        isDestroyed: jest.fn(() => true)
      };
      
      audioController.registerWebContents(destroyedWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(result).toBe(true);
      expect(destroyedWebContents.setAudioMuted).not.toHaveBeenCalled();
    });

    test('should continue with other web contents if one fails', async () => {
      const failingWebContents = {
        setAudioMuted: jest.fn(() => { throw new Error('Test error'); }),
        isDestroyed: jest.fn(() => false)
      };
      
      audioController.registerWebContents(failingWebContents);
      audioController.registerWebContents(mockWebContents);
      
      const result = await audioController.toggleMute();
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });

    test('should handle individual web content errors gracefully', async () => {
      // Create a mock that throws an error
      const errorWebContents = {
        setAudioMuted: jest.fn(() => {
          throw new Error('Test error');
        }),
        isDestroyed: jest.fn(() => false),
        isAudioMuted: jest.fn(() => false)
      };

      audioController.registerWebContents(errorWebContents);

      // The method should not throw, but handle the error gracefully
      const result = await audioController.toggleMute();

      // Should still return the new mute state even if individual web contents fail
      expect(result).toBe(true);
      expect(audioController.isMuted).toBe(true);
    });
  });

  describe('setMute', () => {
    test('should not change state if already in desired state', async () => {
      audioController.isMuted = true;
      
      const result = await audioController.setMute(true);
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).not.toHaveBeenCalled();
    });

    test('should toggle if not in desired state', async () => {
      audioController.registerWebContents(mockWebContents);
      audioController.isMuted = false;
      
      const result = await audioController.setMute(true);
      
      expect(result).toBe(true);
      expect(mockWebContents.setAudioMuted).toHaveBeenCalledWith(true);
    });
  });
});
