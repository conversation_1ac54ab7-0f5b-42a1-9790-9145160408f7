/**
 * YouTube Ad Skipper
 * Detects and automatically skips YouTube ads when the skip button becomes available
 */

class YouTubeAdSkipper {
  constructor() {
    this.isEnabled = true;
    this.checkInterval = null;
    this.adDetectionInterval = 500; // Check every 500ms for faster response
    this.webContents = null;
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    this.isCurrentlySkipping = false;
    this.lastAdDetectionTime = 0;
    this.consecutiveAdDetections = 0;
  }

  /**
   * Register web contents for ad skipping
   * @param {Electron.WebContents} webContents - The YouTube web contents
   */
  registerWebContents(webContents) {
    this.webContents = webContents;
    console.log('YouTube Ad Skipper: Web contents registered');
    this.startAdDetection();
  }

  /**
   * Unregister web contents
   */
  unregisterWebContents() {
    this.stopAdDetection();
    this.webContents = null;
    console.log('YouTube Ad Skipper: Web contents unregistered');
  }

  /**
   * Start the ad detection process
   */
  startAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    if (!this.isEnabled || !this.webContents) {
      return;
    }

    console.log('YouTube Ad Skipper: Starting ad detection');
    
    this.checkInterval = setInterval(() => {
      this.checkForAds();
    }, this.adDetectionInterval);
  }

  /**
   * Stop the ad detection process
   */
  stopAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('YouTube Ad Skipper: Stopped ad detection');
    }
  }

  /**
   * Check for ads and skip them if possible
   */
  async checkForAds() {
    if (!this.webContents || this.webContents.isDestroyed() || this.isCurrentlySkipping) {
      return;
    }

    try {
      const adInfo = await this.webContents.executeJavaScript(`
        (function() {
          // Enhanced ad detection with more comprehensive checks
          const adDetection = {
            // Modern YouTube skip button selectors (2024)
            skipButtons: [
              '.ytp-skip-ad-button',
              '.ytp-ad-skip-button',
              '.ytp-ad-skip-button-modern',
              '.ytp-ad-skip-button-container .ytp-button',
              'button[class*="skip"]',
              'button[aria-label*="Skip"]',
              'button[aria-label*="skip"]',
              'button[data-tooltip-target-id*="skip"]',
              '.ytp-ad-skip-button-slot button',
              '[data-testid="skip-button"]',
              'button:has-text("Skip")',
              'button:has-text("Skip Ad")',
              'button:has-text("Skip Ads")'
            ],

            // Ad presence indicators
            adIndicators: [
              '.ytp-ad-overlay-container',
              '.ytp-ad-text',
              '.ytp-ad-duration-remaining',
              '.ytp-ad-preview-container',
              '.ytp-ad-player-overlay',
              '.ytp-ad-module',
              '.video-ads',
              '.ad-showing',
              '[class*="ad-overlay"]',
              '.ytp-ad-image-overlay'
            ]
          };

          // Check if an ad is currently playing
          let isAdPlaying = false;
          let skipButtonAvailable = false;
          let skipButton = null;
          let adTimeRemaining = null;
          let skipButtonText = '';
          let adType = 'unknown';

          // Method 1: Check for ad overlay elements
          for (const selector of adDetection.adIndicators) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null &&
                getComputedStyle(element).display !== 'none' &&
                getComputedStyle(element).visibility !== 'hidden') {
              isAdPlaying = true;
              adType = 'overlay';
              break;
            }
          }

          // Method 2: Check for "Ad" text in video player area
          if (!isAdPlaying) {
            const videoContainer = document.querySelector('.html5-video-container') ||
                                 document.querySelector('#movie_player') ||
                                 document.querySelector('.ytp-chrome-bottom');

            if (videoContainer) {
              const adTextElements = videoContainer.querySelectorAll('*');
              for (const element of adTextElements) {
                const text = element.textContent || '';
                const trimmedText = text.trim();

                // More specific ad text patterns
                if (trimmedText.match(/^Ad \\d+/) ||
                    trimmedText.includes('Ad ·') ||
                    trimmedText.includes('Advertisement') ||
                    trimmedText.includes('Skip Ad') ||
                    trimmedText.match(/^\\d+:\\d+.*Ad/)) {

                  if (element.offsetParent !== null &&
                      getComputedStyle(element).display !== 'none') {
                    isAdPlaying = true;
                    adType = 'text';
                    break;
                  }
                }
              }
            }
          }

          // Method 3: Check video element properties
          if (!isAdPlaying) {
            const video = document.querySelector('video');
            if (video && video.src) {
              // Check if video URL contains ad indicators
              if (video.src.includes('googleads') ||
                  video.src.includes('doubleclick') ||
                  video.src.includes('/ads/')) {
                isAdPlaying = true;
                adType = 'video-src';
              }
            }
          }

          // Method 4: Check for ad countdown timer
          if (!isAdPlaying) {
            const countdownElements = document.querySelectorAll('*');
            for (const element of countdownElements) {
              const text = element.textContent || '';
              if (text.match(/^\\d+$/) && element.parentElement) {
                const parentText = element.parentElement.textContent || '';
                if (parentText.includes('Ad') || parentText.includes('Skip')) {
                  isAdPlaying = true;
                  adType = 'countdown';
                  break;
                }
              }
            }
          }

          // Look for skip button if ad is detected
          if (isAdPlaying) {
            for (const selector of adDetection.skipButtons) {
              const button = document.querySelector(selector);
              if (button) {
                const style = getComputedStyle(button);
                const isVisible = button.offsetParent !== null &&
                                style.display !== 'none' &&
                                style.visibility !== 'hidden' &&
                                style.opacity !== '0';

                const isClickable = !button.disabled &&
                                  !button.hasAttribute('disabled') &&
                                  !button.classList.contains('disabled');

                // Check if button has actual content
                const hasContent = button.textContent.trim() ||
                                 button.getAttribute('aria-label') ||
                                 button.querySelector('*');

                if (isVisible && isClickable && hasContent) {
                  skipButton = button;
                  skipButtonAvailable = true;
                  skipButtonText = button.textContent.trim() ||
                                 button.getAttribute('aria-label') ||
                                 'Skip';
                  break;
                }
              }
            }

            // Get ad time remaining
            const timeSelectors = [
              '.ytp-ad-duration-remaining',
              '.ytp-ad-text',
              '[class*="countdown"]',
              '[class*="timer"]'
            ];

            for (const selector of timeSelectors) {
              const timeElement = document.querySelector(selector);
              if (timeElement && timeElement.textContent) {
                const text = timeElement.textContent.trim();
                if (text.match(/\\d+/) || text.includes(':')) {
                  adTimeRemaining = text;
                  break;
                }
              }
            }
          }

          return {
            isAdPlaying,
            skipButtonAvailable,
            skipButtonSelector: skipButton ? (skipButton.className || skipButton.tagName) : null,
            skipButtonText,
            adTimeRemaining,
            adType,
            timestamp: Date.now(),
            debug: {
              totalButtons: document.querySelectorAll('button').length,
              skipButtons: adDetection.skipButtons.map(sel => {
                const btn = document.querySelector(sel);
                return btn ? {
                  selector: sel,
                  visible: btn.offsetParent !== null,
                  disabled: btn.disabled,
                  text: btn.textContent.trim()
                } : null;
              }).filter(Boolean)
            }
          };
        })();
      `);

      if (adInfo.isAdPlaying) {
        const currentTime = Date.now();

        // Validate ad detection to reduce false positives
        if (currentTime - this.lastAdDetectionTime < 2000) {
          this.consecutiveAdDetections++;
        } else {
          this.consecutiveAdDetections = 1;
        }
        this.lastAdDetectionTime = currentTime;

        // Only log and act if we have consistent ad detection
        if (this.consecutiveAdDetections >= 2 || adInfo.skipButtonAvailable) {
          console.log('YouTube Ad Skipper: Ad detected', {
            type: adInfo.adType,
            skipAvailable: adInfo.skipButtonAvailable,
            skipButtonText: adInfo.skipButtonText,
            timeRemaining: adInfo.adTimeRemaining,
            consecutiveDetections: this.consecutiveAdDetections
          });

          if (adInfo.skipButtonAvailable) {
            await this.skipAd(adInfo);
          } else if (this.consecutiveAdDetections >= 3) {
            // Log debug info only after multiple consistent detections
            console.log('YouTube Ad Skipper: Skip button not available yet', {
              totalButtons: adInfo.debug.totalButtons,
              skipButtons: adInfo.debug.skipButtons.slice(0, 3) // Limit debug output
            });
          }
        }
      } else {
        // Reset consecutive detections when no ad is detected
        this.consecutiveAdDetections = 0;
      }

    } catch (error) {
      // Silently handle errors to avoid spam in console
      if (error.message.includes('destroyed')) {
        this.stopAdDetection();
      }
    }
  }

  /**
   * Skip the current ad with enhanced detection and clicking
   */
  async skipAd(adInfo = null) {
    if (this.isCurrentlySkipping) {
      return;
    }

    this.isCurrentlySkipping = true;
    const currentTime = Date.now();

    try {
      const skipped = await this.webContents.executeJavaScript(`
        (function() {
          // Enhanced skip button selectors with priority order
          const skipSelectors = [
            '.ytp-skip-ad-button',
            '.ytp-ad-skip-button',
            '.ytp-ad-skip-button-modern',
            '.ytp-ad-skip-button-container .ytp-button',
            'button[class*="skip"]',
            'button[aria-label*="Skip"]',
            'button[aria-label*="skip"]',
            'button[data-tooltip-target-id*="skip"]',
            '.ytp-ad-skip-button-slot button',
            '[data-testid="skip-button"]'
          ];

          // Function to check if element is truly clickable
          function isElementClickable(element) {
            if (!element) return false;

            const style = getComputedStyle(element);
            const rect = element.getBoundingClientRect();

            return element.offsetParent !== null &&
                   style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0' &&
                   !element.disabled &&
                   !element.hasAttribute('disabled') &&
                   !element.classList.contains('disabled') &&
                   rect.width > 0 &&
                   rect.height > 0;
          }

          // Function to simulate human-like click
          function humanClick(element) {
            // Create and dispatch multiple events to simulate real user interaction
            const events = [
              new MouseEvent('mousedown', { bubbles: true, cancelable: true }),
              new MouseEvent('mouseup', { bubbles: true, cancelable: true }),
              new MouseEvent('click', { bubbles: true, cancelable: true })
            ];

            events.forEach(event => {
              element.dispatchEvent(event);
            });

            // Also try direct click
            element.click();

            // Try focus and enter key as backup
            try {
              element.focus();
              element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
              element.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));
            } catch (e) {
              // Ignore keyboard event errors
            }
          }

          // Try each selector in priority order
          for (const selector of skipSelectors) {
            const buttons = document.querySelectorAll(selector);

            for (const button of buttons) {
              if (isElementClickable(button)) {
                const buttonText = button.textContent.trim() ||
                                 button.getAttribute('aria-label') ||
                                 'Unknown';

                console.log('YouTube Ad Skipper: Attempting to click skip button:', {
                  selector: selector,
                  text: buttonText,
                  className: button.className,
                  id: button.id
                });

                // Try multiple click methods
                try {
                  humanClick(button);

                  // Wait a moment and check if ad is still playing
                  setTimeout(() => {
                    const stillHasAd = document.querySelector('.ytp-ad-overlay-container') ||
                                     document.querySelector('.ytp-ad-text') ||
                                     document.querySelector('.ytp-ad-duration-remaining');

                    if (!stillHasAd) {
                      console.log('YouTube Ad Skipper: Ad successfully removed after click');
                    } else {
                      console.log('YouTube Ad Skipper: Ad still present after click, may need retry');
                    }
                  }, 500);

                  return {
                    success: true,
                    method: selector,
                    buttonText: buttonText
                  };

                } catch (clickError) {
                  console.warn('YouTube Ad Skipper: Click failed for button:', clickError.message);
                  continue;
                }
              }
            }
          }

          // If no skip button found, try alternative methods
          console.log('YouTube Ad Skipper: No clickable skip button found, trying alternatives');

          // Method 1: Look for any button containing "skip" text
          const allButtons = document.querySelectorAll('button');
          for (const button of allButtons) {
            const text = (button.textContent || '').toLowerCase();
            const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();

            if ((text.includes('skip') || ariaLabel.includes('skip')) && isElementClickable(button)) {
              console.log('YouTube Ad Skipper: Found alternative skip button by text:', text || ariaLabel);
              try {
                humanClick(button);
                return {
                  success: true,
                  method: 'text-search',
                  buttonText: button.textContent.trim() || ariaLabel
                };
              } catch (e) {
                continue;
              }
            }
          }

          return {
            success: false,
            reason: 'No clickable skip button found',
            totalButtons: allButtons.length,
            skipButtonCandidates: Array.from(allButtons).filter(btn => {
              const text = (btn.textContent || '').toLowerCase();
              const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
              return text.includes('skip') || ariaLabel.includes('skip');
            }).length
          };
        })();
      `);

      if (skipped.success) {
        this.adSkippedCount++;
        this.lastAdSkipTime = currentTime;
        console.log(`YouTube Ad Skipper: Successfully skipped ad #${this.adSkippedCount}`, {
          method: skipped.method,
          buttonText: skipped.buttonText
        });

        // Brief pause after skipping to avoid rapid clicking
        setTimeout(() => {
          this.isCurrentlySkipping = false;
        }, 3000); // Increased to 3 seconds
      } else {
        console.log('YouTube Ad Skipper: Failed to skip ad:', skipped.reason, {
          totalButtons: skipped.totalButtons,
          skipButtonCandidates: skipped.skipButtonCandidates
        });
        this.isCurrentlySkipping = false;
      }

    } catch (error) {
      console.warn('YouTube Ad Skipper: Failed to skip ad:', error.message);
      this.isCurrentlySkipping = false;
    }
  }

  /**
   * Enable ad skipping
   */
  enable() {
    this.isEnabled = true;
    console.log('YouTube Ad Skipper: Enabled');
    if (this.webContents) {
      this.startAdDetection();
    }
  }

  /**
   * Disable ad skipping
   */
  disable() {
    this.isEnabled = false;
    this.stopAdDetection();
    console.log('YouTube Ad Skipper: Disabled');
  }

  /**
   * Get statistics about ad skipping
   */
  getStats() {
    return {
      isEnabled: this.isEnabled,
      adSkippedCount: this.adSkippedCount,
      lastAdSkipTime: this.lastAdSkipTime,
      isCurrentlySkipping: this.isCurrentlySkipping
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    console.log('YouTube Ad Skipper: Statistics reset');
  }
}

module.exports = YouTubeAdSkipper;
