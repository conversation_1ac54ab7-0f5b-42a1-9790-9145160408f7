# YSViewer - YouTube and Spotify Viewer

YSViewer is an Electron-based desktop application that provides automated playlist management for YouTube and Spotify with system-level audio controls and tray functionality.

## Features

- **Dual Platform Support**: Simultaneous YouTube and Spotify playback
- **Automated Playlist Management**: AWS S3 integration for content management
- **System Tray Integration**: Minimize to tray with mute/maximize/minimize controls
- **Audio Controls**: System-level audio management with self-muting capabilities
- **Auto-launch**: Automatic playlist playback on startup
- **Login Management**: Spotify credential saving and automatic login
- **Cookie Consent**: Automatic acceptance of cookie banners for both platforms
- **Cross-platform**: Available for Windows, macOS, and Linux
- **Multiple Distribution Formats**: Both portable and installable versions

## Prerequisites

- Node.js (version 16 or higher)
- npm (comes with Node.js)

## Installation

1. Clone or download this repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

## Running the Application

### Development Mode
To run the application in development mode with debugging enabled:
```bash
npm run dev
```

### Production Mode
To run the application in production mode:
```bash
npm start
```

## Building the Application

### Build for Current Platform
```bash
npm run build
```

### Build for Specific Platforms

#### Windows
```bash
npm run build-win
```
This creates both NSIS installer and portable versions.

#### macOS
```bash
npm run build-mac
```
This creates both DMG and ZIP distributions.

#### Linux
```bash
npm run build-linux
```
This creates both AppImage and DEB packages.

### Package Without Distribution
To create a packaged version without creating installers:
```bash
npm run pack
```

## Application Structure

```
├── src/
│   ├── main.js              # Main Electron process
│   ├── renderer.js          # Renderer process logic
│   ├── electron-index.html  # Main application window
│   ├── audio-controller.js  # System audio management
│   └── playlist-manager.js  # Playlist and AWS S3 integration
├── assets/
│   ├── icon.ico            # Windows icon
│   ├── icon.png            # Linux icon
│   └── create-icon.js      # Icon generation script
├── package.json            # Project configuration and dependencies
└── README.md              # This file
```

## Configuration

The application uses `electron-store` for persistent configuration. Settings are automatically saved and include:

- Spotify login credentials
- Playlist preferences
- Window position and size
- Audio settings
- AWS S3 configuration

## AWS S3 Integration

The application integrates with AWS S3 for playlist content management:

- S3 objects are made public and accessed via HTTPS
- Access keys are only used for uploading content
- Automatic content synchronization

## Tray Functionality

- **Minimize to Tray**: Application minimizes to system tray
- **Tray Controls**: Mute, maximize, minimize options
- **Mini Window**: 50x50 window positioned at bottom-right corner
- **Minimize-to-dot**: Compact tray representation

## Audio Management

- **Self-muting**: Application mutes itself rather than system audio
- **System Integration**: Native audio control integration
- **Parallel Playback**: Both YouTube and Spotify can play simultaneously

## Auto-launch

The application can be configured to:
- Start automatically with the system
- Begin playlist playback on startup
- Restore previous session state

## Troubleshooting

### Common Issues

1. **Application won't start**: Ensure Node.js and npm are properly installed
2. **Build fails**: Try deleting `node_modules` and running `npm install` again
3. **Audio issues**: Check system audio permissions and settings
4. **Tray not appearing**: Restart the application or check system tray settings

### Logs

Application logs are stored in the standard Electron log directory:
- **Windows**: `%USERPROFILE%\AppData\Roaming\YSViewer\logs`
- **macOS**: `~/Library/Logs/YSViewer`
- **Linux**: `~/.config/YSViewer/logs`

## Development

### Project Scripts

- `npm start`: Run the application
- `npm run dev`: Run in development mode
- `npm run build`: Build for current platform
- `npm run build-win`: Build for Windows
- `npm run build-mac`: Build for macOS
- `npm run build-linux`: Build for Linux
- `npm run pack`: Package without distribution
- `npm run dist`: Create distribution packages

### Dependencies

#### Production Dependencies
- `electron`: Desktop application framework
- `aws-sdk`: AWS S3 integration
- `node-fetch`: HTTP requests
- `electron-store`: Persistent storage
- `auto-launch`: System startup integration

#### Development Dependencies
- `electron-builder`: Application packaging and distribution

## License

MIT License - see package.json for details

## Support

For issues and feature requests, please check the application logs and ensure all prerequisites are met before reporting problems.
